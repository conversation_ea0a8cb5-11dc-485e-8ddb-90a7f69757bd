#!/usr/bin/env python3
"""
调试班级学生列表为空的问题
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'aispeak-server'))

from sqlalchemy.orm import Session
from app.db import get_db
from app.db.task_entities import Class, ClassStudent
from app.db.account_entities import AccountEntity

def debug_class_students():
    """调试班级25的学生数据"""
    db = next(get_db())
    
    class_id = 25
    
    print(f"=== 调试班级 {class_id} 的学生数据 ===")
    
    # 1. 检查班级是否存在
    print("\n1. 检查班级是否存在:")
    db_class = db.query(Class).filter(
        Class.id == class_id,
        Class.deleted_at.is_(None)
    ).first()
    
    if db_class:
        print(f"   班级存在: ID={db_class.id}, 名称={db_class.name}, 状态={db_class.status}")
        print(f"   教师ID: {db_class.teacher_id}")
    else:
        print(f"   班级 {class_id} 不存在或已删除")
        return
    
    # 2. 检查 class_students 表中的数据
    print(f"\n2. 检查 class_students 表中班级 {class_id} 的所有记录:")
    all_class_students = db.query(ClassStudent).filter(
        ClassStudent.class_id == class_id
    ).all()
    
    print(f"   总记录数: {len(all_class_students)}")
    for cs in all_class_students:
        print(f"   - ID: {cs.id}, 学生ID: {cs.student_id}, 状态: {cs.status}, 加入时间: {cs.join_date}")
    
    # 3. 检查活跃学生记录
    print(f"\n3. 检查班级 {class_id} 的活跃学生记录:")
    active_class_students = db.query(ClassStudent).filter(
        ClassStudent.class_id == class_id,
        ClassStudent.status == "active"
    ).all()
    
    print(f"   活跃学生记录数: {len(active_class_students)}")
    for cs in active_class_students:
        print(f"   - ID: {cs.id}, 学生ID: {cs.student_id}, 状态: {cs.status}")
    
    # 4. 检查学生账户是否存在
    print(f"\n4. 检查学生账户信息:")
    if active_class_students:
        student_ids = [cs.student_id for cs in active_class_students]
        students = db.query(AccountEntity).filter(
            AccountEntity.id.in_(student_ids)
        ).all()
        
        print(f"   找到的学生账户数: {len(students)}")
        for student in students:
            print(f"   - 学生ID: {student.id}, 用户名: {student.user_name}, 手机: {student.phone_number}, 角色: {student.user_role}")
    
    # 5. 执行完整的JOIN查询（与代码中相同）
    print(f"\n5. 执行完整的JOIN查询:")
    students_join = db.query(AccountEntity).join(
        ClassStudent, AccountEntity.id == ClassStudent.student_id
    ).filter(
        ClassStudent.class_id == class_id,
        ClassStudent.status == "active"
    ).all()
    
    print(f"   JOIN查询结果数: {len(students_join)}")
    for student in students_join:
        print(f"   - 学生ID: {student.id}, 用户名: {student.user_name}, 手机: {student.phone_number}")
    
    # 6. 检查是否有其他状态的学生
    print(f"\n6. 检查所有状态的学生记录:")
    all_statuses = db.query(ClassStudent.status).filter(
        ClassStudent.class_id == class_id
    ).distinct().all()

    print(f"   班级 {class_id} 中的所有状态: {[status[0] for status in all_statuses]}")

    # 7. 检查是否有deleted_at不为空的记录
    print(f"\n7. 检查是否有软删除的记录:")
    deleted_students = db.query(ClassStudent).filter(
        ClassStudent.class_id == class_id,
        ClassStudent.deleted_at.is_not(None)
    ).all()

    print(f"   软删除的学生记录数: {len(deleted_students)}")
    for cs in deleted_students:
        print(f"   - ID: {cs.id}, 学生ID: {cs.student_id}, 删除时间: {cs.deleted_at}")

    # 8. 检查account表中是否存在这个学生ID
    print(f"\n8. 详细检查account表:")
    if active_class_students:
        for cs in active_class_students:
            student_id = cs.student_id
            print(f"   查找学生ID: {student_id}")

            # 精确查找
            account = db.query(AccountEntity).filter(
                AccountEntity.id == student_id
            ).first()

            if account:
                print(f"   ✓ 找到账户: ID={account.id}, 用户名={account.user_name}")
            else:
                print(f"   ✗ 未找到账户")

                # 检查是否有类似的ID
                similar_accounts = db.query(AccountEntity).filter(
                    AccountEntity.id.like(f"%{student_id[-10:]}%")
                ).all()

                print(f"   类似ID的账户数: {len(similar_accounts)}")
                for acc in similar_accounts[:5]:  # 只显示前5个
                    print(f"     - ID: {acc.id}, 用户名: {acc.user_name}")

    # 9. 检查account表中所有记录的ID格式
    print(f"\n9. 检查account表中的ID格式:")
    sample_accounts = db.query(AccountEntity).limit(10).all()
    print(f"   样本账户数: {len(sample_accounts)}")
    for acc in sample_accounts:
        print(f"   - ID: {acc.id}, 类型: {type(acc.id)}, 长度: {len(acc.id)}")

    # 10. 检查是否有openid匹配的情况
    print(f"\n10. 检查openid匹配:")
    if active_class_students:
        for cs in active_class_students:
            student_id = cs.student_id
            account_by_openid = db.query(AccountEntity).filter(
                AccountEntity.openid == student_id
            ).first()

            if account_by_openid:
                print(f"   ✓ 通过openid找到账户: ID={account_by_openid.id}, openid={account_by_openid.openid}")
            else:
                print(f"   ✗ 通过openid未找到账户")

if __name__ == "__main__":
    debug_class_students()
